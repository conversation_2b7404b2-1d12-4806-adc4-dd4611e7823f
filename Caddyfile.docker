# CNInfo 年报分析系统 - Docker 环境 Caddy 配置
# 适配 Docker Compose 部署

{$DOMAIN:localhost:8080} {
    # 前端静态文件服务
    root * /var/www/html
    file_server
    
    # API 反向代理到后端容器
    handle /api/* {
        reverse_proxy cninfo-backend:5000 {
            # 健康检查
            health_uri /api/health
            health_interval 30s
            health_timeout 10s
            health_status 200
            
            # 请求头
            header_up Host {host}
            header_up X-Real-IP {remote_host}
            header_up X-Forwarded-For {remote_host}
            header_up X-Forwarded-Proto {scheme}
            
            # 超时设置
            transport http {
                dial_timeout 10s
                response_header_timeout 60s
                read_timeout 120s
                write_timeout 120s
            }
            
            # 错误处理
            handle_response {
                @backend_error status 5xx
                @backend_timeout status 504
                @backend_unavailable status 502 503
                
                respond @backend_error "后端服务错误" 500 {
                    header Content-Type "application/json; charset=utf-8"
                    body `{"error": "Backend service error", "message": "后端服务出现错误，请稍后重试"}`
                }
                respond @backend_timeout "请求超时" 504 {
                    header Content-Type "application/json; charset=utf-8"
                    body `{"error": "Request timeout", "message": "请求超时，请稍后重试"}`
                }
                respond @backend_unavailable "服务不可用" 503 {
                    header Content-Type "application/json; charset=utf-8"
                    body `{"error": "Service unavailable", "message": "后端服务暂时不可用"}`
                }
            }
        }
    }
    
    # 流式AI分析接口特殊处理
    handle /api/ai_analysis_stream {
        reverse_proxy cninfo-backend:5000 {
            # 流式响应配置
            flush_interval -1
            
            # 增加超时时间支持长时间AI分析
            transport http {
                dial_timeout 10s
                response_header_timeout 120s
                read_timeout 600s  # 10分钟
                write_timeout 600s # 10分钟
            }
            
            header_up Host {host}
            header_up X-Real-IP {remote_host}
            header_up X-Forwarded-For {remote_host}
            header_up X-Forwarded-Proto {scheme}
        }
    }
    
    # 文件导出接口
    handle /api/export* {
        reverse_proxy cninfo-backend:5000 {
            transport http {
                dial_timeout 10s
                response_header_timeout 60s
                read_timeout 300s  # 5分钟
                write_timeout 300s # 5分钟
            }
            
            header_up Host {host}
            header_up X-Real-IP {remote_host}
            header_up X-Forwarded-For {remote_host}
            header_up X-Forwarded-Proto {scheme}
        }
    }
    
    # SPA 路由支持
    @spa {
        not path /api/*
        not path *.js *.css *.png *.jpg *.jpeg *.gif *.ico *.svg *.woff *.woff2 *.ttf *.eot *.map
        file {
            try_files {path} /index.html
        }
    }
    rewrite @spa /index.html
    
    # 压缩配置
    encode {
        gzip 6
        zstd
        match {
            header Content-Type text/*
            header Content-Type application/json*
            header Content-Type application/javascript*
            header Content-Type application/xml*
            header Content-Type image/svg+xml*
        }
    }
    
    # 静态资源缓存
    @static_long {
        path *.js *.css *.woff *.woff2 *.ttf *.eot
        path */assets/*
    }
    header @static_long {
        Cache-Control "public, max-age=31536000, immutable"
        Vary "Accept-Encoding"
    }
    
    @static_short {
        path *.png *.jpg *.jpeg *.gif *.ico *.svg
    }
    header @static_short {
        Cache-Control "public, max-age=86400"
        Vary "Accept-Encoding"
    }
    
    # HTML 不缓存
    @html {
        path *.html /
    }
    header @html {
        Cache-Control "no-cache, no-store, must-revalidate"
        Pragma "no-cache"
        Expires "0"
    }
    
    # API 不缓存
    @api {
        path /api/*
    }
    header @api {
        Cache-Control "no-cache, no-store, must-revalidate"
        Pragma "no-cache"
    }
    
    # 安全头
    header {
        X-Content-Type-Options nosniff
        X-Frame-Options SAMEORIGIN
        X-XSS-Protection "1; mode=block"
        Referrer-Policy "strict-origin-when-cross-origin"
        
        # 移除服务器信息
        -Server
        
        # 如果是HTTPS环境，启用HSTS
        @https {
            header_regexp Host ^https://
        }
        Strict-Transport-Security "max-age=31536000; includeSubDomains" @https
    }
    
    # CORS 支持（如果需要）
    @cors_preflight {
        method OPTIONS
    }
    respond @cors_preflight 204 {
        header Access-Control-Allow-Origin "*"
        header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
        header Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With"
        header Access-Control-Max-Age "86400"
    }
    
    # 访问日志
    log access {
        output file /var/log/caddy/access.log {
            roll_size 100mb
            roll_keep 5
            roll_keep_for 720h
        }
        format json {
            time_format "2006-01-02T15:04:05.000Z07:00"
        }
        level INFO
    }
    
    # 错误日志
    log error {
        output file /var/log/caddy/error.log {
            roll_size 50mb
            roll_keep 3
            roll_keep_for 168h
        }
        format json
        level WARN
    }
}

# 健康检查端点
:2020 {
    respond /health 200 {
        body "OK"
        header Content-Type "text/plain"
    }
    
    # Caddy 管理接口
    admin localhost:2019
}
