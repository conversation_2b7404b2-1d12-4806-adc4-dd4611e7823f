# CNInfo 年报分析系统 - 完整部署配置（包含 Caddy）
version: '3.8'

services:
  # 后端服务
  cninfo-backend:
    build: ./backend
    container_name: cninfo-backend
    environment:
      - FLASK_ENV=production
      - FLASK_HOST=0.0.0.0
      - FLASK_PORT=5000
      - FLASK_DEBUG=false
      - PYTHONUNBUFFERED=1
      # OpenAI 配置（可选）
      - OPENAI_API_KEY=${OPENAI_API_KEY:-}
      - OPENAI_BASE_URL=${OPENAI_BASE_URL:-https://api.openai.com/v1}
      - OPENAI_MODEL=${OPENAI_MODEL:-gpt-3.5-turbo}
    volumes:
      # 数据持久化
      - ./data/txt:/app/txt
      - ./data/pdf:/app/pdf
      - ./data/database:/app/database
      - ./data/exports:/app/exports
      - ./data/results:/app/results
    networks:
      - cninfo-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    depends_on: []

  # 前端构建（临时容器）
  cninfo-frontend-build:
    image: node:18-alpine
    container_name: cninfo-frontend-build
    working_dir: /app
    volumes:
      - ./frontend:/app
      - frontend-dist:/app/dist
    command: >
      sh -c "
        npm ci --only=production &&
        npm run build
      "
    environment:
      - NODE_ENV=production
      - VITE_API_URL=
    networks:
      - cninfo-network

  # Caddy 反向代理
  cninfo-caddy:
    image: caddy:2-alpine
    container_name: cninfo-caddy
    ports:
      - "80:80"
      - "443:443"
      - "2019:2019"  # Caddy 管理接口
    volumes:
      # Caddy 配置
      - ./Caddyfile.docker:/etc/caddy/Caddyfile:ro
      # 前端静态文件
      - frontend-dist:/var/www/html:ro
      # SSL 证书持久化
      - caddy-data:/data
      - caddy-config:/config
      # 日志目录
      - ./logs/caddy:/var/log/caddy
    environment:
      - DOMAIN=${DOMAIN:-localhost:8080}
    networks:
      - cninfo-network
    restart: unless-stopped
    depends_on:
      cninfo-frontend-build:
        condition: service_completed_successfully
      cninfo-backend:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:2020/health"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  cninfo-network:
    driver: bridge
    name: cninfo-network

volumes:
  frontend-dist:
    name: cninfo-frontend-dist
  caddy-data:
    name: cninfo-caddy-data
  caddy-config:
    name: cninfo-caddy-config
