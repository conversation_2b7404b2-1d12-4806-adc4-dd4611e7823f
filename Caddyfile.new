# CNInfo 年报分析系统 Caddy 配置文件
# 适配 Flask 后端 + React 前端的完整部署方案

# 主站点配置
{$DOMAIN:localhost} {
    # 前端静态文件服务
    root * {$FRONTEND_ROOT:./frontend/dist}
    file_server
    
    # API 反向代理到后端服务
    handle /api/* {
        reverse_proxy {$BACKEND_HOST:localhost}:{$BACKEND_PORT:5000} {
            # 健康检查配置
            health_uri /api/health
            health_interval 30s
            health_timeout 10s
            health_status 200
            
            # 请求头处理
            header_up Host {host}
            header_up X-Real-IP {remote_host}
            header_up X-Forwarded-For {remote_host}
            header_up X-Forwarded-Proto {scheme}
            header_up X-Forwarded-Host {host}
            
            # 超时设置
            transport http {
                dial_timeout 10s
                response_header_timeout 60s
                read_timeout 120s
                write_timeout 120s
            }
            
            # 错误处理
            handle_response {
                @5xx status 5xx
                @timeout status 504
                @unavailable status 502 503
                
                respond @5xx "后端服务内部错误，请稍后重试" 500 {
                    header Content-Type "text/plain; charset=utf-8"
                }
                respond @timeout "请求超时，请稍后重试" 504 {
                    header Content-Type "text/plain; charset=utf-8"
                }
                respond @unavailable "后端服务暂时不可用，请检查服务状态" 503 {
                    header Content-Type "text/plain; charset=utf-8"
                }
            }
        }
    }
    
    # 处理流式 AI 分析接口（特殊处理）
    handle /api/ai_analysis_stream {
        reverse_proxy {$BACKEND_HOST:localhost}:{$BACKEND_PORT:5000} {
            # 流式响应不设置缓冲
            flush_interval -1
            
            # 增加超时时间以支持长时间的AI分析
            transport http {
                dial_timeout 10s
                response_header_timeout 120s
                read_timeout 300s
                write_timeout 300s
            }
            
            header_up Host {host}
            header_up X-Real-IP {remote_host}
            header_up X-Forwarded-For {remote_host}
            header_up X-Forwarded-Proto {scheme}
        }
    }
    
    # 文件下载接口（PDF/Excel导出）
    handle /api/export* {
        reverse_proxy {$BACKEND_HOST:localhost}:{$BACKEND_PORT:5000} {
            # 文件下载需要更长的超时时间
            transport http {
                dial_timeout 10s
                response_header_timeout 60s
                read_timeout 180s
                write_timeout 180s
            }
            
            header_up Host {host}
            header_up X-Real-IP {remote_host}
            header_up X-Forwarded-For {remote_host}
            header_up X-Forwarded-Proto {scheme}
        }
    }
    
    # SPA 路由支持 - 所有非 API 和静态资源请求都返回 index.html
    @spa {
        not path /api/*
        not path *.js *.css *.png *.jpg *.jpeg *.gif *.ico *.svg *.woff *.woff2 *.ttf *.eot *.map
        file {
            try_files {path} /index.html
        }
    }
    rewrite @spa /index.html
    
    # 启用压缩
    encode {
        gzip 6
        zstd
        # 压缩文件类型
        match {
            header Content-Type text/*
            header Content-Type application/json*
            header Content-Type application/javascript*
            header Content-Type application/xml*
            header Content-Type application/rss+xml*
            header Content-Type application/atom+xml*
            header Content-Type image/svg+xml*
        }
    }
    
    # 静态资源缓存策略
    @static_assets {
        path *.js *.css *.png *.jpg *.jpeg *.gif *.ico *.svg *.woff *.woff2 *.ttf *.eot
    }
    header @static_assets {
        Cache-Control "public, max-age=31536000, immutable"
        Vary "Accept-Encoding"
    }
    
    # 带版本号的静态资源（强缓存）
    @versioned_assets {
        path *.*.js *.*.css
        path *-*.js *-*.css
        path */assets/*
    }
    header @versioned_assets {
        Cache-Control "public, max-age=31536000, immutable"
        Vary "Accept-Encoding"
    }
    
    # HTML 文件缓存策略（不缓存）
    @html {
        path *.html /
    }
    header @html {
        Cache-Control "no-cache, no-store, must-revalidate"
        Pragma "no-cache"
        Expires "0"
    }
    
    # API 响应缓存策略
    @api_responses {
        path /api/*
    }
    header @api_responses {
        Cache-Control "no-cache, no-store, must-revalidate"
        Pragma "no-cache"
    }
    
    # 安全头设置
    header {
        # 基础安全头
        X-Content-Type-Options nosniff
        X-Frame-Options SAMEORIGIN
        X-XSS-Protection "1; mode=block"
        Referrer-Policy "strict-origin-when-cross-origin"
        
        # 权限策略
        Permissions-Policy "geolocation=(), microphone=(), camera=(), payment=(), usb=(), magnetometer=(), gyroscope=(), accelerometer=()"
        
        # HTTPS 强制（仅在 HTTPS 环境下）
        Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
        
        # 内容安全策略（根据需要调整）
        Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob:; font-src 'self' data:; connect-src 'self' ws: wss:; frame-ancestors 'self';"
        
        # 移除服务器信息
        -Server
    }
    
    # CORS 处理（如果需要跨域访问）
    @cors_preflight {
        method OPTIONS
    }
    respond @cors_preflight 204 {
        header Access-Control-Allow-Origin "*"
        header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
        header Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With"
        header Access-Control-Max-Age "86400"
    }
    
    # 访问日志配置
    log access {
        output file {$LOG_DIR:/var/log/caddy}/cninfo-access.log {
            roll_size 100mb
            roll_keep 10
            roll_keep_for 720h
        }
        format json {
            time_format "2006-01-02T15:04:05.000Z07:00"
            message_key "message"
            level_key "level"
            time_key "timestamp"
        }
        level INFO
    }
    
    # 错误日志配置
    log error {
        output file {$LOG_DIR:/var/log/caddy}/cninfo-error.log {
            roll_size 50mb
            roll_keep 5
            roll_keep_for 168h
        }
        format json
        level ERROR
    }
}

# HTTP 重定向到 HTTPS（生产环境）
http://{$DOMAIN} {
    redir https://{$DOMAIN}{uri} permanent
}

# 开发环境配置（可选）
:3000 {
    reverse_proxy localhost:3000
    log {
        level DEBUG
    }
}

# 管理接口（可选，用于监控）
:2019 {
    metrics /metrics
    admin localhost:2019
}
