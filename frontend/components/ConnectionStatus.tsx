'use client'

import { useState, useEffect, useRef } from 'react'
import {
  Wifi,
  WifiOff,
  RefreshCw,
  CheckCircle,
  XCircle,
  AlertTriangle,
  ChevronDown,
  ChevronUp
} from 'lucide-react'
import { Button } from './ui/Button'
import { Badge } from './ui/Badge'
import { apiMethods } from '@/lib/api'
import { cn } from '@/lib/utils'

interface ConnectionStatus {
  status: 'checking' | 'connected' | 'disconnected' | 'error'
  message: string
  latency?: number
  timestamp?: Date
}

export function ConnectionStatus() {
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>({
    status: 'disconnected',
    message: '未检测'
  })
  const [isChecking, setIsChecking] = useState(false)
  const [isExpanded, setIsExpanded] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)

  // 点击外部区域关闭弹窗和ESC键关闭
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsExpanded(false)
      }
    }

    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setIsExpanded(false)
      }
    }

    if (isExpanded) {
      document.addEventListener('mousedown', handleClickOutside)
      document.addEventListener('keydown', handleEscapeKey)
      return () => {
        document.removeEventListener('mousedown', handleClickOutside)
        document.removeEventListener('keydown', handleEscapeKey)
      }
    }
  }, [isExpanded])

  // 自动检测连接状态
  useEffect(() => {
    checkConnection()
    // 每30秒自动检测一次
    const interval = setInterval(checkConnection, 30000)
    return () => clearInterval(interval)
  }, [])

  const checkConnection = async () => {
    setIsChecking(true)
    setConnectionStatus(prev => ({
      ...prev,
      status: 'checking',
      message: '正在检测连接...'
    }))

    const startTime = Date.now()

    try {
      const response = await apiMethods.healthCheck()
      const latency = Date.now() - startTime

      if (response.status === 200) {
        const data = response.data
        setConnectionStatus({
          status: 'connected',
          message: data.data?.message || '后端连接正常',
          latency,
          timestamp: new Date()
        })
      } else {
        setConnectionStatus({
          status: 'error',
          message: `HTTP ${response.status}: ${response.statusText}`,
          timestamp: new Date()
        })
      }
    } catch (error: any) {
      const latency = Date.now() - startTime
      let message = '连接失败'
      
      if (error.code === 'ECONNREFUSED') {
        message = '后端服务器未启动'
      } else if (error.code === 'ECONNABORTED') {
        message = '连接超时'
      } else if (error.response?.status === 404) {
        message = 'API接口不存在'
      } else if (error.response?.status === 500) {
        message = '服务器内部错误'
      } else if (error.message) {
        message = error.message
      }

      setConnectionStatus({
        status: 'disconnected',
        message,
        latency,
        timestamp: new Date()
      })
    } finally {
      setIsChecking(false)
    }
  }

  const getStatusIcon = () => {
    switch (connectionStatus.status) {
      case 'checking':
        return <RefreshCw className="w-4 h-4 text-blue-600 animate-spin" />
      case 'connected':
        return <CheckCircle className="w-4 h-4 text-green-600" />
      case 'disconnected':
        return <WifiOff className="w-4 h-4 text-red-600" />
      case 'error':
        return <XCircle className="w-4 h-4 text-red-600" />
      default:
        return <AlertTriangle className="w-4 h-4 text-yellow-600" />
    }
  }

  const getStatusBadge = () => {
    switch (connectionStatus.status) {
      case 'checking':
        return <Badge variant="info" size="sm">检测中</Badge>
      case 'connected':
        return <Badge variant="success" size="sm">已连接</Badge>
      case 'disconnected':
        return <Badge variant="error" size="sm">未连接</Badge>
      case 'error':
        return <Badge variant="error" size="sm">错误</Badge>
      default:
        return <Badge variant="warning" size="sm">未知</Badge>
    }
  }

  const getStatusColor = () => {
    switch (connectionStatus.status) {
      case 'connected':
        return 'text-green-600'
      case 'disconnected':
      case 'error':
        return 'text-red-600'
      case 'checking':
        return 'text-blue-600'
      default:
        return 'text-yellow-600'
    }
  }

  return (
    <div className="relative" ref={containerRef}>
      {/* 状态指示器 */}
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className={cn(
          'flex items-center space-x-2 px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-200',
          connectionStatus.status === 'connected' && 'bg-green-50 text-green-700 hover:bg-green-100',
          connectionStatus.status === 'disconnected' && 'bg-red-50 text-red-700 hover:bg-red-100',
          connectionStatus.status === 'error' && 'bg-red-50 text-red-700 hover:bg-red-100',
          connectionStatus.status === 'checking' && 'bg-blue-50 text-blue-700 hover:bg-blue-100'
        )}
      >
        {getStatusIcon()}
        <span className="hidden sm:inline">
          {connectionStatus.status === 'connected' && '已连接'}
          {connectionStatus.status === 'disconnected' && '未连接'}
          {connectionStatus.status === 'error' && '错误'}
          {connectionStatus.status === 'checking' && '检测中'}
        </span>
        {isExpanded ? (
          <ChevronUp className="w-3 h-3" />
        ) : (
          <ChevronDown className="w-3 h-3" />
        )}
      </button>

      {/* 详细信息浮动窗口 */}
      {isExpanded && (
        <div className="absolute top-full right-0 mt-2 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-50 animate-in fade-in slide-in-from-top-2 duration-200">
          <div className="p-4 space-y-4">
            {/* 状态头部 */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                {getStatusIcon()}
                <span className="font-medium text-gray-900">后端连接状态</span>
              </div>
              {getStatusBadge()}
            </div>

            {/* 状态消息 */}
            <div className="p-3 bg-gray-50 rounded-lg">
              <p className={cn('text-sm font-medium', getStatusColor())}>
                {connectionStatus.message}
              </p>
            </div>

            {/* 详细信息 */}
            {connectionStatus.latency !== undefined && (
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="font-medium text-gray-700">响应时间</p>
                  <p className="text-gray-900">{connectionStatus.latency}ms</p>
                </div>
                {connectionStatus.timestamp && (
                  <div>
                    <p className="font-medium text-gray-700">检测时间</p>
                    <p className="text-gray-600">
                      {connectionStatus.timestamp.toLocaleTimeString()}
                    </p>
                  </div>
                )}
              </div>
            )}

            {/* 连接信息 */}
            <div className="bg-gray-50 rounded-lg p-3">
              <h4 className="text-sm font-medium text-gray-700 mb-2">连接信息</h4>
              <div className="space-y-1 text-xs text-gray-600">
                <div>后端地址: http://localhost:5000</div>
                <div>API前缀: /api</div>
                <div>超时时间: 30秒</div>
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex items-center justify-between pt-2 border-t border-gray-200">
              <Button
                variant="ghost"
                size="sm"
                onClick={checkConnection}
                disabled={isChecking}
                loading={isChecking}
              >
                <RefreshCw className="w-4 h-4 mr-1" />
                重新检测
              </Button>

              {connectionStatus.status === 'disconnected' && (
                <div className="text-xs text-red-600">
                  请确保后端服务器已启动
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
