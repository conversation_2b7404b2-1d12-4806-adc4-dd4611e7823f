# CNInfo 年报分析系统 - Caddy 部署环境变量配置

# 域名配置
DOMAIN=cninfo.example.com
# 本地开发使用: DOMAIN=localhost:8080

# 后端服务配置
BACKEND_HOST=localhost
BACKEND_PORT=5000

# 前端构建配置
FRONTEND_ROOT=./frontend/dist

# 日志目录
LOG_DIR=./logs

# OpenAI 配置（可选）
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-3.5-turbo

# Flask 配置
FLASK_ENV=production
FLASK_HOST=0.0.0.0
FLASK_PORT=5000
FLASK_DEBUG=false

# 数据库配置
DATABASE_PATH=./data/database/cninfo_reports.db

# 文件路径配置
TXT_DIR=./data/txt
PDF_DIR=./data/pdf
EXPORTS_DIR=./data/exports
RESULTS_DIR=./data/results

# 安全配置
SECRET_KEY=your_secret_key_here_change_in_production

# 爬虫配置
REQUEST_TIMEOUT=30
REQUEST_DELAY=1.0
MAX_RETRIES=3

# AI 分析配置
CONTEXT_LENGTH=300
MAX_CONTEXTS=10
AI_ANALYSIS_TIMEOUT=300

# 分页配置
DEFAULT_PAGE_SIZE=20
MAX_PAGE_SIZE=100
